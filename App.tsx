import React, { useState, useEffect } from 'react';
import { Header } from './components/Header';
import { ChatInterface } from './components/ChatInterface';
import { UserInput } from './components/UserInput';
import { WorkflowTracker } from './components/WorkflowTracker';
import { ThinkingSpace } from './components/ThinkingSpace';
import { FinalOutput } from './components/FinalOutput';
import { ModelMonitor } from './components/ModelMonitor';
import LanguageComplianceMonitor from './components/LanguageComplianceMonitor';
import TranslationMonitor from './components/TranslationMonitor';
import { EngagementPrompts } from './components/EngagementPrompts';
import { UserGuidance } from './components/UserGuidance';
import { ReportGenerator } from './components/ReportGenerator';
import { FinalActionPlanGenerator } from './components/FinalActionPlanGenerator';
import { NotificationManager } from './components/UpdateNotification';
import SlideFooter from './components/SlideFooter';
import ApiKeyStats from './components/ApiKeyStats';
import ProfileBackup from './components/ProfileBackup';
import BackupNotification from './components/BackupNotification';
import { sendMessageToAI, generateSystemPrompt, generateFinalOutput } from './services/geminiService';
import { profileBackupService } from './services/profileBackupService';
import { workflowMemoryService } from './services/workflowMemoryService';
import { WORKFLOW_STEPS } from './constants';
import type { Message } from './types';

const App: React.FC = () => {
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [isProcessing, setIsProcessing] = useState(false);
  const [initialProblem, setInitialProblem] = useState('');
  const [conversation, setConversation] = useState<Message[]>([]);
  const [reasoningLog, setReasoningLog] = useState<string[]>([]);
  const [finalPrompt, setFinalPrompt] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [showApiStats, setShowApiStats] = useState(false);

  useEffect(() => {
    if (currentStepIndex === WORKFLOW_STEPS.length - 1 && !finalPrompt && !isProcessing) {
      generateFinalPrompt();
    }
  }, [currentStepIndex, finalPrompt, isProcessing]);

  const generateFinalPrompt = async () => {
      setIsProcessing(true);
      setReasoningLog(prev => [...prev, "Génération du prompt final et de la méta-analyse..."]);
      try {
          const fullConversation = [...conversation];
          const result = await generateFinalOutput(fullConversation);
          setFinalPrompt(result);
          setCurrentStepIndex(prev => prev + 1); // Mark the final step as complete
      } catch (e) {
          console.error(e);
          const errorMessage = e instanceof Error ? e.message : 'Une erreur inconnue est survenue.';
          setError(`Erreur lors de la génération de la sortie finale : ${errorMessage}`);
      } finally {
          setIsProcessing(false);
      }
  };

  const handleStartWorkflow = async (problem: string) => {
    setInitialProblem(problem);
    setCurrentStepIndex(0);
    setError(null);
    setFinalPrompt(null);
    setReasoningLog([]);

    const introMessage: Message = { sender: 'ai', text: "Excellent ! Commençons à analyser votre défi. Étape 1 : Définition du Problème." };
    setConversation([introMessage]);

    // Sauvegarder le nouveau workflow dans le profil utilisateur
    profileBackupService.addWorkflow({
      problemDescription: problem,
      stepsCompleted: 0,
      totalSteps: WORKFLOW_STEPS.length
    });

    await processMessage(problem, 0, [introMessage]);
  };

  const handleStartNewWorkflow = () => {
    // Réinitialiser complètement l'application pour un nouveau workflow
    setInitialProblem(null);
    setCurrentStepIndex(0);
    setError(null);
    setFinalPrompt(null);
    setReasoningLog([]);
    setConversation([]);
    // Réinitialiser la mémoire du workflow
    workflowMemoryService.reset();
  };

  const handleRefinePrompt = () => {
    // Revenir au workflow pour affiner le prompt existant
    setFinalPrompt(null);
    setCurrentStepIndex(WORKFLOW_STEPS.length - 2); // Revenir à l'avant-dernière étape

    const refineMessage: Message = {
      sender: 'ai',
      text: "Parfait ! Reprenons le processus pour affiner votre prompt. Que souhaitez-vous améliorer ou préciser dans la solution actuelle ?"
    };
    setConversation(prev => [...prev, refineMessage]);
  };

  const handleSendMessage = async (messageText: string) => {
    if (isProcessing || currentStepIndex >= WORKFLOW_STEPS.length - 1) return;

    const newUserMessage: Message = { sender: 'user', text: messageText };
    const updatedConversation = [...conversation, newUserMessage];
    setConversation(updatedConversation);

    // Sauvegarder le message utilisateur
    profileBackupService.addConversation(newUserMessage);

    await processMessage(messageText, currentStepIndex, updatedConversation);
  };

  const handleSuggestedAction = (suggestion: string) => {
    handleSendMessage(suggestion);
  };

  const processMessage = async (userInput: string, stepIndex: number, currentConversation: Message[]) => {
    setIsProcessing(true);
    setError(null);

    try {
        const systemPrompt = generateSystemPrompt(stepIndex, initialProblem || userInput);
        
        const apiConversation = [...currentConversation];
        // For the initial call from handleStartWorkflow, the user's problem isn't in the conversation history yet. Add it for the API call.
        if (stepIndex === 0) {
            apiConversation.push({ sender: 'user', text: userInput });
        }

        const messagesForApi = [
            { role: 'system', content: systemPrompt },
            ...apiConversation.map(m => ({
                role: m.sender === 'user' ? 'user' : 'assistant',
                content: m.text
            }))
        ];

        const task = WORKFLOW_STEPS[stepIndex].task;
        const { content, modelUsed } = await sendMessageToAI(messagesForApi, task);

        setReasoningLog(prev => [...prev, `Étape ${stepIndex + 1}: Modèle utilisé - ${modelUsed}`]);
        
        const newAiMessage: Message = { sender: 'ai', text: content };

        // Sauvegarder la réponse de l'IA
        profileBackupService.addConversation(newAiMessage);

        // Analyser et stocker l'insight pour le rapport final
        const currentStep = WORKFLOW_STEPS[stepIndex];
        if (currentStep) {
            workflowMemoryService.analyzeConversationStep(
                stepIndex,
                currentStep.title,
                userInput,
                content
            );
        }

        // For the initial step, add both the user's problem and the AI's response to the chat.
        // For subsequent steps, the user's message is already added by handleSendMessage.
        if (stepIndex === 0) {
            const userMessage: Message = { sender: 'user', text: userInput };
            profileBackupService.addConversation(userMessage);
            setConversation(prev => [...prev, userMessage, newAiMessage]);
        } else {
            setConversation(prev => [...prev, newAiMessage]);
        }

        setCurrentStepIndex(prev => prev + 1);

    } catch (e) {
        console.error(e);
        const errorMessage = e instanceof Error ? e.message : 'Une erreur inconnue est survenue.';
        setError(`Une erreur de communication avec l'IA est survenue. ${errorMessage}. Veuillez vérifier votre clé API et réessayer.`);
        const errorAiMessage: Message = { sender: 'ai', text: `Désolé, une erreur est survenue : ${errorMessage}`};
        // Only add the AI error message to the conversation to avoid confusion.
        setConversation(prev => [...prev, errorAiMessage]);
    } finally {
        setIsProcessing(false);
    }
  };


  return (
    <NotificationManager>
      <div
        className="h-screen w-screen text-gray-200 font-sans flex flex-col overflow-hidden"
        style={{
          backgroundColor: '#0f172a', // Fond sombre pour éviter le blanc avec PNG transparent
          backgroundImage: 'url(/Background.png)',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
          backgroundAttachment: 'fixed'
        }}
      >
      <Header />
      <main className="flex-grow flex container mx-auto p-4 gap-4 overflow-hidden">
        {finalPrompt ? (
          // Mode plein écran pour le résultat final
          <div className="flex-grow flex flex-col bg-slate-800/50 rounded-2xl shadow-2xl border border-slate-700 overflow-hidden">
            <FinalOutput
              finalPrompt={finalPrompt}
              onStartNewWorkflow={handleStartNewWorkflow}
              onRefinePrompt={handleRefinePrompt}
              initialProblem={initialProblem}
              conversation={conversation}
              reasoningLog={reasoningLog}
              currentStepIndex={currentStepIndex}
            />
          </div>
        ) : initialProblem ? (
          // Layout 3 colonnes pour le workflow actif
          <>
            {/* COLONNE GAUCHE - Progression + Suggestions (25%) */}
            <aside className="w-1/4 flex flex-col gap-4">
              <div className="bg-slate-800/50 rounded-2xl shadow-lg border border-slate-700 overflow-hidden">
                <WorkflowTracker steps={WORKFLOW_STEPS} currentStepIndex={currentStepIndex} />
              </div>

              <div className="bg-slate-800/50 rounded-2xl shadow-lg border border-slate-700 p-4">
                <UserGuidance
                  currentStep={currentStepIndex}
                  totalSteps={WORKFLOW_STEPS.length}
                  isProcessing={isProcessing}
                />
              </div>

              {/* Suggestions d'engagement */}
              {!isProcessing && currentStepIndex < WORKFLOW_STEPS.length - 1 && currentStepIndex > 0 && (
                <div className="bg-slate-800/50 rounded-2xl shadow-lg border border-slate-700 p-4">
                  <EngagementPrompts
                    currentStep={currentStepIndex}
                    totalSteps={WORKFLOW_STEPS.length}
                    onSuggestedAction={handleSuggestedAction}
                  />
                </div>
              )}

              {/* Générateur de rapport stratégique */}
              {initialProblem && currentStepIndex > 1 && (
                <div className="bg-slate-800/50 rounded-2xl shadow-lg border border-slate-700 p-4">
                  <ReportGenerator
                    initialProblem={initialProblem}
                    conversation={conversation}
                    reasoningLog={reasoningLog}
                    currentStepIndex={currentStepIndex}
                  />
                </div>
              )}
            </aside>

            {/* COLONNE CENTRE - Chat + Input (50%) */}
            <div className="flex-1 flex flex-col bg-slate-800/50 rounded-2xl shadow-2xl border border-slate-700 overflow-hidden">
              <ChatInterface conversation={conversation} isProcessing={isProcessing} />
              <div className="p-4 border-t border-slate-700">
                {error && <p className="text-red-400 text-center mb-2">{error}</p>}
                <UserInput
                  isProcessing={isProcessing}
                  onSendMessage={handleSendMessage}
                  isWorkflowStarted={true}
                  isWorkflowComplete={currentStepIndex >= WORKFLOW_STEPS.length - 1}
                />
              </div>
            </div>

            {/* COLONNE DROITE - Raisonnement + Modèles (25%) */}
            <aside className="w-1/4 flex flex-col gap-4">
              <ThinkingSpace log={reasoningLog} className="flex-grow" />
              <ModelMonitor className="flex-shrink-0" />
              <LanguageComplianceMonitor className="flex-shrink-0" />
              <TranslationMonitor className="flex-shrink-0" />

              {/* Générateur de Plan d'Action Final */}
              <FinalActionPlanGenerator
                initialProblem={initialProblem || ''}
                currentStepIndex={currentStepIndex}
                className="flex-shrink-0"
              />

              {/* Composant de sauvegarde du profil utilisateur */}
              <ProfileBackup className="flex-shrink-0" />

              {/* Bouton pour afficher les statistiques des clés API */}
              <div className="flex-shrink-0">
                <button
                  onClick={() => setShowApiStats(true)}
                  className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 shadow-lg"
                >
                  📊 Statistiques API
                </button>
              </div>
              <footer className="flex-shrink-0 text-center py-2">
                <a href="https://flexodiv.com" target="_blank" rel="noopener noreferrer" className="inline-block">
                  <img src="https://res.cloudinary.com/dte5zykne/image/upload/v1750807840/02-Logo-FlexoDiv_uoxcao.png" alt="Logo FlexoDiv" className="h-10 mx-auto opacity-60 hover:opacity-100 transition-opacity" />
                </a>
              </footer>
            </aside>
          </>
        ) : (
          // Écran d'accueil
          <div className="flex-grow flex flex-col justify-center items-center p-8 text-center bg-slate-800/50 rounded-2xl shadow-2xl border border-slate-700">
            <h2 className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-indigo-500 mb-4">
              Bienvenue dans Roony - Studio Agentique
            </h2>
            <p className="max-w-2xl text-slate-400">
              Décrivez votre problème ou objectif complexe ci-dessous. Notre agent IA vous guidera à travers un processus structuré pour analyser, innover et générer une solution puissante et sur mesure.
            </p>
            <div className="p-4 mt-4 w-full max-w-2xl">
               <UserInput
                  isProcessing={isProcessing}
                  onSendMessage={handleStartWorkflow}
                  isWorkflowStarted={false}
                  isWorkflowComplete={false}
                />
            </div>
          </div>
        )}
      </main>
      <SlideFooter />

      {/* Modal des statistiques API */}
      <ApiKeyStats
        isVisible={showApiStats}
        onClose={() => setShowApiStats(false)}
      />

      {/* Notification de sauvegarde */}
      <BackupNotification />
      </div>
    </NotificationManager>
  );
};

export default App;